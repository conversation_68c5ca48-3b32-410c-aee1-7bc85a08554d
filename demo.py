"""
联邦学习演示脚本
快速演示联邦学习的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
from src.models.cnn_model import create_model
from src.data.data_loader import FederatedDataLoader
from src.federated.client import create_clients
from src.federated.server import FederatedServer


def quick_demo():
    """
    快速演示联邦学习
    """
    print("=" * 50)
    print("联邦学习快速演示")
    print("=" * 50)
    
    # 设置参数
    num_clients = 3
    num_rounds = 5
    local_epochs = 1
    
    print(f"配置: {num_clients}个客户端, {num_rounds}轮训练, 每轮本地训练{local_epochs}个epoch")
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 1. 创建数据加载器
    print("\n1. 准备数据...")
    data_loader = FederatedDataLoader(
        num_clients=num_clients,
        batch_size=64,
        iid=False  # 非独立同分布
    )
    
    # 显示数据分布
    data_loader.print_data_distribution()
    
    # 2. 创建客户端
    print("\n2. 创建客户端...")
    clients = create_clients(
        model_class=create_model,
        data_loader=data_loader,
        num_clients=num_clients,
        device=device,
        lr=0.01
    )
    
    for i, client in enumerate(clients):
        print(f"  客户端 {i}: {client.get_data_size()} 个训练样本")
    
    # 3. 创建服务器
    print("\n3. 创建服务器...")
    server = FederatedServer(create_model, device=device)
    
    # 4. 获取测试数据
    test_dataloader = data_loader.get_test_dataloader()
    print(f"测试集大小: {len(test_dataloader.dataset)} 个样本")
    
    # 5. 初始评估
    print("\n4. 初始模型评估...")
    initial_result = server.evaluate_global_model(test_dataloader)
    print(f"初始准确率: {initial_result['test_accuracy']:.2f}%")
    
    # 6. 联邦训练
    print(f"\n5. 开始联邦训练 ({num_rounds} 轮)...")
    training_summary = server.federated_training(
        clients=clients,
        test_dataloader=test_dataloader,
        num_rounds=num_rounds,
        local_epochs=local_epochs,
        client_fraction=1.0,
        verbose=True
    )
    
    # 7. 结果总结
    print("\n" + "=" * 50)
    print("演示结果总结")
    print("=" * 50)
    print(f"初始准确率: {initial_result['test_accuracy']:.2f}%")
    print(f"最终准确率: {training_summary['final_accuracy']:.2f}%")
    print(f"准确率提升: {training_summary['final_accuracy'] - initial_result['test_accuracy']:.2f}%")
    print(f"最佳准确率: {training_summary['best_accuracy']:.2f}% (第{training_summary['best_round']}轮)")
    
    # 8. 显示训练历史
    print(f"\n训练历史:")
    for i, acc in enumerate(training_summary['accuracy_history']):
        print(f"  第{i+1}轮: {acc:.2f}%")
    
    print("\n演示完成!")
    return training_summary


def test_individual_components():
    """
    测试各个组件
    """
    print("\n" + "=" * 50)
    print("组件测试")
    print("=" * 50)
    
    # 测试模型
    print("\n1. 测试模型...")
    model = create_model()
    print(f"模型参数数量: {model.get_model_size():,}")
    
    # 测试前向传播
    x = torch.randn(1, 1, 28, 28)
    output = model(x)
    print(f"输入形状: {x.shape}, 输出形状: {output.shape}")
    
    # 测试数据加载器
    print("\n2. 测试数据加载器...")
    data_loader = FederatedDataLoader(num_clients=2, batch_size=32, iid=True)
    
    # 获取客户端数据
    client_0_loader = data_loader.get_client_dataloader(0)
    print(f"客户端0数据批次数: {len(client_0_loader)}")
    
    # 测试一个批次
    for batch_data, batch_labels in client_0_loader:
        print(f"批次数据形状: {batch_data.shape}, 标签形状: {batch_labels.shape}")
        break
    
    # 测试客户端
    print("\n3. 测试客户端...")
    from src.federated.client import FederatedClient
    
    client = FederatedClient(
        client_id=0,
        model=create_model(),
        dataloader=client_0_loader,
        device='cpu'
    )
    
    print(f"客户端数据大小: {client.get_data_size()}")
    
    # 测试本地训练
    print("进行1个epoch的本地训练...")
    result = client.train_local_model(epochs=1, verbose=False)
    print(f"训练结果 - 损失: {result['final_loss']:.4f}, 准确率: {result['final_accuracy']:.2f}%")
    
    print("\n组件测试完成!")


if __name__ == "__main__":
    # 运行演示
    try:
        # 快速演示
        training_summary = quick_demo()
        
        # 组件测试
        test_individual_components()
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        print("请检查依赖是否正确安装")
        
        # 显示安装命令
        print("\n如果出现依赖问题，请运行:")
        print("pip install -r requirements.txt")
