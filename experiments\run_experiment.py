"""
联邦学习实验主脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

from src.models.cnn_model import create_model
from src.data.data_loader import FederatedDataLoader
from src.federated.client import create_clients
from src.federated.server import FederatedServer
from src.utils.visualization import FederatedLearningVisualizer
from src.utils.evaluation import FederatedLearningEvaluator


def run_federated_experiment(config):
    """
    运行联邦学习实验
    
    Args:
        config: 实验配置字典
    """
    print("=" * 60)
    print("联邦学习实验开始")
    print("=" * 60)
    
    # 设置随机种子
    torch.manual_seed(config['seed'])
    np.random.seed(config['seed'])
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['use_gpu'] else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    print("\n1. 准备数据...")
    data_loader = FederatedDataLoader(
        num_clients=config['num_clients'],
        batch_size=config['batch_size'],
        iid=config['iid']
    )
    
    # 打印数据分布
    data_loader.print_data_distribution()
    
    # 创建客户端
    print("\n2. 创建客户端...")
    clients = create_clients(
        model_class=create_model,
        data_loader=data_loader,
        num_clients=config['num_clients'],
        device=device,
        lr=config['learning_rate']
    )
    
    print(f"成功创建 {len(clients)} 个客户端")
    for i, client in enumerate(clients):
        print(f"  客户端 {i}: {client.get_data_size()} 个样本")
    
    # 创建服务器
    print("\n3. 创建服务器...")
    server = FederatedServer(create_model, device=device)
    
    # 获取测试数据
    test_dataloader = data_loader.get_test_dataloader()
    
    # 运行联邦训练
    print("\n4. 开始联邦训练...")
    training_summary = server.federated_training(
        clients=clients,
        test_dataloader=test_dataloader,
        num_rounds=config['num_rounds'],
        local_epochs=config['local_epochs'],
        client_fraction=config['client_fraction'],
        verbose=True
    )
    
    return server, training_summary, data_loader


def run_centralized_experiment(data_loader, config):
    """
    运行集中式学习实验（用于对比）
    
    Args:
        data_loader: 数据加载器
        config: 实验配置
    """
    print("\n" + "=" * 60)
    print("集中式学习对比实验")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() and config['use_gpu'] else 'cpu')
    
    # 创建集中式数据加载器（合并所有客户端数据）
    all_indices = []
    for i in range(config['num_clients']):
        all_indices.extend(data_loader.client_data_indices[i])
    
    from torch.utils.data import Subset, DataLoader
    centralized_dataset = Subset(data_loader.train_dataset, all_indices)
    centralized_dataloader = DataLoader(
        centralized_dataset, 
        batch_size=config['batch_size'], 
        shuffle=True
    )
    
    # 创建模型
    model = create_model().to(device)
    criterion = torch.nn.CrossEntropyLoss()
    optimizer = torch.optim.SGD(model.parameters(), lr=config['learning_rate'], momentum=0.9)
    
    # 训练
    model.train()
    total_epochs = config['num_rounds'] * config['local_epochs']
    
    print(f"集中式训练 {total_epochs} 个epoch...")
    
    for epoch in range(total_epochs):
        running_loss = 0.0
        correct = 0
        total = 0
        
        for data, target in centralized_dataloader:
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
        
        if (epoch + 1) % 5 == 0:
            accuracy = 100. * correct / total
            print(f"Epoch {epoch+1}/{total_epochs}: Loss: {running_loss/len(centralized_dataloader):.4f}, "
                  f"Accuracy: {accuracy:.2f}%")
    
    # 评估
    test_dataloader = data_loader.get_test_dataloader()
    model.eval()
    test_loss = 0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in test_dataloader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            test_loss += criterion(output, target).item()
            
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
    
    centralized_accuracy = 100. * correct / total
    centralized_loss = test_loss / len(test_dataloader)
    
    print(f"集中式学习最终测试准确率: {centralized_accuracy:.2f}%")
    print(f"集中式学习最终测试损失: {centralized_loss:.4f}")
    
    return {
        'accuracy': centralized_accuracy,
        'loss': centralized_loss
    }


def save_results(federated_summary, centralized_result, config, timestamp):
    """
    保存实验结果
    """
    results = {
        'timestamp': timestamp,
        'config': config,
        'federated_learning': federated_summary,
        'centralized_learning': centralized_result
    }
    
    # 保存到JSON文件
    results_file = f"results/experiment_results_{timestamp}.json"
    os.makedirs('results', exist_ok=True)
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n实验结果已保存到: {results_file}")
    return results_file


def main():
    """
    主函数
    """
    # 实验配置
    config = {
        'num_clients': 3,
        'num_rounds': 20,
        'local_epochs': 2,
        'batch_size': 32,
        'learning_rate': 0.01,
        'client_fraction': 1.0,
        'iid': False,  # 非独立同分布
        'use_gpu': True,
        'seed': 42
    }
    
    print("实验配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 运行联邦学习实验
    server, federated_summary, data_loader = run_federated_experiment(config)
    
    # 运行集中式学习对比实验
    centralized_result = run_centralized_experiment(data_loader, config)
    
    # 结果对比
    print("\n" + "=" * 60)
    print("实验结果对比")
    print("=" * 60)
    print(f"联邦学习最终准确率: {federated_summary['final_accuracy']:.2f}%")
    print(f"集中式学习最终准确率: {centralized_result['accuracy']:.2f}%")
    print(f"准确率差异: {federated_summary['final_accuracy'] - centralized_result['accuracy']:.2f}%")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = save_results(federated_summary, centralized_result, config, timestamp)
    
    # 保存模型
    model_file = f"results/federated_model_{timestamp}.pth"
    server.save_model(model_file)

    # 创建可视化和评估
    print("\n5. 生成可视化和评估报告...")
    visualizer = FederatedLearningVisualizer(save_dir='results/plots')
    evaluator = FederatedLearningEvaluator()

    # 绘制训练曲线
    visualizer.plot_training_curves(federated_summary, centralized_result,
                                   save_name=f'experiment_{timestamp}')

    # 绘制数据分布
    visualizer.plot_data_distribution(data_loader, save_name=f'experiment_{timestamp}')

    # 绘制客户端性能
    visualizer.plot_client_performance(federated_summary, save_name=f'experiment_{timestamp}')

    # 绘制收敛分析
    visualizer.plot_convergence_analysis(federated_summary, save_name=f'experiment_{timestamp}')

    # 创建总结报告
    visualizer.create_summary_report(federated_summary, centralized_result, config,
                                   save_name=f'experiment_{timestamp}')

    # 生成详细评估报告
    test_dataloader = data_loader.get_test_dataloader()
    evaluation_report = evaluator.generate_evaluation_report(
        federated_summary, centralized_result, server.get_global_model(),
        test_dataloader, save_dir='results'
    )

    print(f"\n实验完成! 结果文件: {results_file}")
    print(f"模型文件: {model_file}")
    print(f"可视化图表保存在: results/plots/")
    print(f"评估报告保存在: results/")


if __name__ == "__main__":
    main()
