"""
项目结构测试脚本
验证所有模块是否可以正确导入
"""

import sys
import os

def test_imports():
    """
    测试所有模块导入
    """
    print("测试项目模块导入...")
    
    try:
        # 测试模型模块
        print("1. 测试模型模块...")
        from src.models.cnn_model import create_model, SimpleCNN
        print("   ✅ 模型模块导入成功")
        
        # 测试数据模块
        print("2. 测试数据模块...")
        from src.data.data_loader import FederatedDataLoader
        print("   ✅ 数据模块导入成功")
        
        # 测试联邦学习模块
        print("3. 测试联邦学习模块...")
        from src.federated.client import FederatedClient, create_clients
        from src.federated.server import FederatedServer
        from src.federated.fedavg import FedAvg
        print("   ✅ 联邦学习模块导入成功")
        
        # 测试工具模块
        print("4. 测试工具模块...")
        from src.utils.visualization import FederatedLearningVisualizer
        from src.utils.evaluation import FederatedLearningEvaluator
        print("   ✅ 工具模块导入成功")
        
        print("\n所有模块导入测试通过! ✅")
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False


def test_basic_functionality():
    """
    测试基本功能
    """
    print("\n测试基本功能...")
    
    try:
        # 测试模型创建
        print("1. 测试模型创建...")
        from src.models.cnn_model import create_model
        model = create_model()
        print(f"   模型参数数量: {model.get_model_size():,}")
        print("   ✅ 模型创建成功")
        
        # 测试数据加载器创建
        print("2. 测试数据加载器创建...")
        from src.data.data_loader import FederatedDataLoader
        data_loader = FederatedDataLoader(num_clients=2, batch_size=32, iid=True)
        print(f"   客户端数量: {data_loader.num_clients}")
        print("   ✅ 数据加载器创建成功")
        
        # 测试服务器创建
        print("3. 测试服务器创建...")
        from src.federated.server import FederatedServer
        server = FederatedServer(create_model)
        print("   ✅ 服务器创建成功")
        
        # 测试可视化工具创建
        print("4. 测试可视化工具创建...")
        from src.utils.visualization import FederatedLearningVisualizer
        visualizer = FederatedLearningVisualizer()
        print("   ✅ 可视化工具创建成功")
        
        print("\n基本功能测试通过! ✅")
        return True
        
    except Exception as e:
        print(f"   ❌ 功能测试错误: {e}")
        return False


def check_project_structure():
    """
    检查项目结构
    """
    print("检查项目结构...")
    
    required_files = [
        'README.md',
        'requirements.txt',
        'demo.py',
        'experiments/run_experiment.py',
        'src/models/cnn_model.py',
        'src/data/data_loader.py',
        'src/federated/client.py',
        'src/federated/server.py',
        'src/federated/fedavg.py',
        'src/utils/visualization.py',
        'src/utils/evaluation.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"   ✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少文件: {missing_files}")
        return False
    else:
        print(f"\n项目结构检查通过! ✅")
        return True


def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("联邦学习项目测试")
    print("=" * 60)
    
    # 检查项目结构
    structure_ok = check_project_structure()
    
    if not structure_ok:
        print("项目结构不完整，请检查文件")
        return
    
    # 测试导入
    import_ok = test_imports()
    
    if not import_ok:
        print("模块导入失败，请检查依赖")
        print("运行: pip install -r requirements.txt")
        return
    
    # 测试基本功能
    function_ok = test_basic_functionality()
    
    if function_ok:
        print("\n" + "=" * 60)
        print("🎉 项目测试全部通过!")
        print("=" * 60)
        print("项目已准备就绪，可以运行以下命令:")
        print("1. 快速演示: python demo.py")
        print("2. 完整实验: python experiments/run_experiment.py")
    else:
        print("\n❌ 基本功能测试失败")


if __name__ == "__main__":
    main()
