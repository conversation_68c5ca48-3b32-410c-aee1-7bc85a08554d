# 基于MNIST的联邦学习实验

## 项目简介

本项目实现了一个基于MNIST手写数字识别的联邦学习系统，模拟三个终端设备（如三所不同学校）分别拥有本地数据，通过联邦平均（FedAvg）算法进行协作训练，在不共享原始数据的情况下提升模型性能。

## 实验场景

- **参与方**: 3个客户端（模拟不同的终端设备）
- **数据集**: MNIST手写数字识别数据集
- **算法**: 联邦平均（FedAvg）算法
- **模型**: 简单的卷积神经网络（CNN）

## 项目结构

```
Fedral/
├── src/
│   ├── models/
│   │   └── cnn_model.py          # CNN模型定义
│   ├── federated/
│   │   ├── client.py             # 客户端实现
│   │   ├── server.py             # 服务器端实现
│   │   └── fedavg.py             # FedAvg算法实现
│   ├── data/
│   │   └── data_loader.py        # 数据处理和分发
│   └── utils/
│       ├── visualization.py      # 可视化工具
│       └── evaluation.py         # 评估工具
├── experiments/
│   └── run_experiment.py         # 主实验脚本
├── results/                      # 实验结果存储
├── requirements.txt              # 依赖包
└── README.md                     # 项目说明
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 快速演示
```bash
python demo.py
```

### 3. 完整实验
```bash
python experiments/run_experiment.py
```

## 实验特点

1. **数据隐私保护**: 各客户端数据不离开本地
2. **非均匀数据分布**: 模拟真实场景中的数据异构性
3. **联邦平均算法**: 实现经典的FedAvg算法
4. **结果可视化**: 提供训练过程和结果的可视化分析

## 实验结果

实验将比较以下几种情况的模型性能：
- 联邦学习模型 vs 集中式学习模型
- 不同轮数训练的精度变化
- 不同客户端数据分布对性能的影响

## 实验输出

运行完整实验后，将生成以下文件：
- `results/experiment_results_*.json`: 实验数据和配置
- `results/federated_model_*.pth`: 训练好的联邦学习模型
- `results/plots/`: 可视化图表目录
  - 训练曲线对比图
  - 数据分布图
  - 客户端性能图
  - 收敛分析图
  - 实验总结报告
- `results/confusion_matrix.png`: 混淆矩阵
- `results/class_performance.png`: 各类别性能图

## 主要特性

### 1. 联邦学习核心功能
- ✅ FedAvg算法实现
- ✅ 多客户端模拟
- ✅ 非IID数据分布
- ✅ 模型参数聚合

### 2. 实验对比
- ✅ 联邦学习 vs 集中式学习
- ✅ 不同轮数训练效果
- ✅ 客户端性能分析

### 3. 可视化分析
- ✅ 训练过程可视化
- ✅ 数据分布可视化
- ✅ 性能对比图表
- ✅ 收敛性分析

### 4. 详细评估
- ✅ 混淆矩阵
- ✅ 分类报告
- ✅ 各类别性能分析
- ✅ 训练效率分析

## 扩展功能建议

- 隐私保护机制（差分隐私）
- 自适应学习率调整
- 更复杂的数据异构性模拟
- 客户端选择策略
- 通信成本分析
