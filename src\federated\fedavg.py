"""
联邦平均（FedAvg）算法实现
"""

import torch
import copy
from collections import OrderedDict
import numpy as np


class FedAvg:
    """
    联邦平均算法实现
    """
    
    def __init__(self, global_model):
        """
        初始化FedAvg算法
        
        Args:
            global_model: 全局模型
        """
        self.global_model = global_model
        self.global_parameters = global_model.get_model_parameters()
        
    def aggregate_parameters(self, client_parameters_list, client_weights=None):
        """
        聚合客户端参数
        
        Args:
            client_parameters_list: 客户端参数列表
            client_weights: 客户端权重（默认为平均权重）
        
        Returns:
            聚合后的全局参数
        """
        if not client_parameters_list:
            raise ValueError("客户端参数列表不能为空")
        
        num_clients = len(client_parameters_list)
        
        # 如果没有指定权重，使用平均权重
        if client_weights is None:
            client_weights = [1.0 / num_clients] * num_clients
        
        # 确保权重和为1
        total_weight = sum(client_weights)
        client_weights = [w / total_weight for w in client_weights]
        
        # 初始化聚合参数
        aggregated_parameters = OrderedDict()
        
        # 获取参数名称
        param_names = client_parameters_list[0].keys()
        
        # 对每个参数进行加权平均
        for param_name in param_names:
            # 初始化为零张量
            aggregated_param = torch.zeros_like(client_parameters_list[0][param_name])
            
            # 加权求和
            for client_idx, client_params in enumerate(client_parameters_list):
                weight = client_weights[client_idx]
                aggregated_param += weight * client_params[param_name]
            
            aggregated_parameters[param_name] = aggregated_param
        
        return aggregated_parameters
    
    def aggregate_weighted_by_data_size(self, client_parameters_list, client_data_sizes):
        """
        根据数据大小加权聚合参数
        
        Args:
            client_parameters_list: 客户端参数列表
            client_data_sizes: 客户端数据大小列表
        
        Returns:
            聚合后的全局参数
        """
        total_data_size = sum(client_data_sizes)
        client_weights = [size / total_data_size for size in client_data_sizes]
        
        return self.aggregate_parameters(client_parameters_list, client_weights)
    
    def update_global_model(self, aggregated_parameters):
        """
        更新全局模型参数
        
        Args:
            aggregated_parameters: 聚合后的参数
        """
        self.global_parameters = aggregated_parameters
        self.global_model.set_model_parameters(aggregated_parameters)
    
    def get_global_parameters(self):
        """
        获取全局模型参数
        """
        return copy.deepcopy(self.global_parameters)
    
    def federated_averaging_round(self, clients, selected_clients=None, weight_by_data_size=True):
        """
        执行一轮联邦平均
        
        Args:
            clients: 客户端列表
            selected_clients: 选中的客户端索引（如果为None则选择所有客户端）
            weight_by_data_size: 是否根据数据大小加权
        
        Returns:
            聚合结果信息
        """
        if selected_clients is None:
            selected_clients = list(range(len(clients)))
        
        # 收集客户端参数
        client_parameters_list = []
        client_data_sizes = []
        
        for client_idx in selected_clients:
            client = clients[client_idx]
            client_parameters_list.append(client.get_model_parameters())
            client_data_sizes.append(client.get_data_size())
        
        # 聚合参数
        if weight_by_data_size:
            aggregated_parameters = self.aggregate_weighted_by_data_size(
                client_parameters_list, client_data_sizes
            )
        else:
            aggregated_parameters = self.aggregate_parameters(client_parameters_list)
        
        # 更新全局模型
        self.update_global_model(aggregated_parameters)
        
        return {
            'selected_clients': selected_clients,
            'client_data_sizes': client_data_sizes,
            'total_data_size': sum(client_data_sizes),
            'aggregated_parameters': aggregated_parameters
        }
    
    def calculate_parameter_difference(self, params1, params2):
        """
        计算两组参数之间的差异（L2范数）
        
        Args:
            params1: 参数组1
            params2: 参数组2
        
        Returns:
            参数差异的L2范数
        """
        total_diff = 0.0
        
        for param_name in params1.keys():
            if param_name in params2:
                diff = params1[param_name] - params2[param_name]
                total_diff += torch.norm(diff).item() ** 2
        
        return np.sqrt(total_diff)
    
    def get_model_convergence_info(self, previous_parameters):
        """
        获取模型收敛信息
        
        Args:
            previous_parameters: 上一轮的参数
        
        Returns:
            收敛信息字典
        """
        if previous_parameters is None:
            return {'parameter_change': None, 'converged': False}
        
        parameter_change = self.calculate_parameter_difference(
            self.global_parameters, previous_parameters
        )
        
        # 简单的收敛判断（可以根据需要调整阈值）
        convergence_threshold = 1e-6
        converged = parameter_change < convergence_threshold
        
        return {
            'parameter_change': parameter_change,
            'converged': converged,
            'convergence_threshold': convergence_threshold
        }


def create_fedavg_server(model_class):
    """
    创建FedAvg服务器
    
    Args:
        model_class: 模型类
    
    Returns:
        FedAvg实例
    """
    global_model = model_class()
    return FedAvg(global_model)


if __name__ == "__main__":
    # 测试FedAvg算法
    from src.models.cnn_model import create_model
    
    # 创建全局模型
    global_model = create_model()
    fedavg = FedAvg(global_model)
    
    # 模拟客户端参数
    client_params_1 = global_model.get_model_parameters()
    client_params_2 = global_model.get_model_parameters()
    
    # 添加一些随机变化
    for param_name in client_params_1.keys():
        client_params_1[param_name] += torch.randn_like(client_params_1[param_name]) * 0.01
        client_params_2[param_name] += torch.randn_like(client_params_2[param_name]) * 0.01
    
    # 测试聚合
    client_parameters_list = [client_params_1, client_params_2]
    aggregated = fedavg.aggregate_parameters(client_parameters_list)
    
    print("FedAvg算法测试完成")
    print(f"聚合参数数量: {len(aggregated)}")
