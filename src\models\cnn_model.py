"""
CNN模型定义
用于MNIST手写数字识别的简单卷积神经网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class SimpleCNN(nn.Module):
    """
    简单的CNN模型，适用于MNIST数据集
    """
    
    def __init__(self, num_classes=10):
        super(SimpleCNN, self).__init__()
        
        # 第一个卷积层
        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, stride=1, padding=1)
        self.bn1 = nn.BatchNorm2d(32)
        
        # 第二个卷积层
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(64)
        
        # 第三个卷积层
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1)
        self.bn3 = nn.BatchNorm2d(128)
        
        # 池化层
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)
        
        # 全连接层
        self.fc1 = nn.Linear(128 * 3 * 3, 256)
        self.dropout1 = nn.Dropout(0.5)
        self.fc2 = nn.Linear(256, 128)
        self.dropout2 = nn.Dropout(0.5)
        self.fc3 = nn.Linear(128, num_classes)
        
    def forward(self, x):
        # 第一个卷积块
        x = self.pool(F.relu(self.bn1(self.conv1(x))))
        
        # 第二个卷积块
        x = self.pool(F.relu(self.bn2(self.conv2(x))))
        
        # 第三个卷积块
        x = self.pool(F.relu(self.bn3(self.conv3(x))))
        
        # 展平
        x = x.view(-1, 128 * 3 * 3)
        
        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = self.fc3(x)
        
        return x
    
    def get_model_parameters(self):
        """
        获取模型参数（用于联邦学习）
        """
        return {name: param.data.clone() for name, param in self.named_parameters()}
    
    def set_model_parameters(self, parameters):
        """
        设置模型参数（用于联邦学习）
        """
        for name, param in self.named_parameters():
            if name in parameters:
                param.data.copy_(parameters[name])
    
    def get_model_size(self):
        """
        获取模型大小（参数数量）
        """
        return sum(p.numel() for p in self.parameters())


def create_model(num_classes=10):
    """
    创建模型实例
    """
    return SimpleCNN(num_classes=num_classes)


if __name__ == "__main__":
    # 测试模型
    model = create_model()
    print(f"模型参数数量: {model.get_model_size():,}")
    
    # 测试前向传播
    x = torch.randn(1, 1, 28, 28)
    output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
