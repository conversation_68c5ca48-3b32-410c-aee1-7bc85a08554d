"""
联邦学习服务器端实现
"""

import torch
import copy
import numpy as np
from tqdm import tqdm
from .fedavg import FedAvg


class FederatedServer:
    """
    联邦学习服务器
    """
    
    def __init__(self, model_class, device='cpu'):
        """
        初始化服务器
        
        Args:
            model_class: 模型类
            device: 计算设备
        """
        self.device = device
        self.global_model = model_class().to(device)
        self.fedavg = FedAvg(self.global_model)
        
        # 训练历史
        self.round_history = []
        self.global_test_accuracies = []
        self.global_test_losses = []
        
    def federated_training(self, clients, test_dataloader, num_rounds=10, 
                          local_epochs=1, client_fraction=1.0, verbose=True):
        """
        执行联邦训练
        
        Args:
            clients: 客户端列表
            test_dataloader: 测试数据加载器
            num_rounds: 联邦训练轮数
            local_epochs: 本地训练轮数
            client_fraction: 每轮参与的客户端比例
            verbose: 是否显示详细信息
        """
        num_clients = len(clients)
        clients_per_round = max(1, int(client_fraction * num_clients))
        
        if verbose:
            print(f"开始联邦训练:")
            print(f"  总轮数: {num_rounds}")
            print(f"  客户端数量: {num_clients}")
            print(f"  每轮参与客户端数: {clients_per_round}")
            print(f"  本地训练轮数: {local_epochs}")
            print("=" * 50)
        
        previous_global_params = None
        
        for round_num in range(num_rounds):
            if verbose:
                print(f"\n联邦训练轮次 {round_num + 1}/{num_rounds}")
            
            # 选择参与本轮训练的客户端
            selected_clients = np.random.choice(
                num_clients, clients_per_round, replace=False
            ).tolist()
            
            if verbose:
                print(f"选中的客户端: {selected_clients}")
            
            # 将全局模型参数分发给选中的客户端
            global_params = self.fedavg.get_global_parameters()
            for client_idx in selected_clients:
                clients[client_idx].set_model_parameters(global_params)
            
            # 客户端本地训练
            client_results = []
            for client_idx in selected_clients:
                if verbose:
                    print(f"  客户端 {client_idx} 开始本地训练...")
                
                result = clients[client_idx].train_local_model(
                    epochs=local_epochs, 
                    verbose=False
                )
                client_results.append(result)
                
                if verbose:
                    print(f"    最终损失: {result['final_loss']:.4f}, "
                          f"准确率: {result['final_accuracy']:.2f}%")
            
            # 服务器聚合
            aggregation_result = self.fedavg.federated_averaging_round(
                clients, selected_clients, weight_by_data_size=True
            )
            
            # 评估全局模型
            global_test_result = self.evaluate_global_model(test_dataloader)
            
            # 计算收敛信息
            convergence_info = self.fedavg.get_model_convergence_info(previous_global_params)
            previous_global_params = copy.deepcopy(self.fedavg.get_global_parameters())
            
            # 记录历史
            round_info = {
                'round': round_num + 1,
                'selected_clients': selected_clients,
                'client_results': client_results,
                'global_test_accuracy': global_test_result['test_accuracy'],
                'global_test_loss': global_test_result['test_loss'],
                'convergence_info': convergence_info,
                'aggregation_result': aggregation_result
            }
            
            self.round_history.append(round_info)
            self.global_test_accuracies.append(global_test_result['test_accuracy'])
            self.global_test_losses.append(global_test_result['test_loss'])
            
            if verbose:
                print(f"  全局模型测试准确率: {global_test_result['test_accuracy']:.2f}%")
                print(f"  全局模型测试损失: {global_test_result['test_loss']:.4f}")
                if convergence_info['parameter_change'] is not None:
                    print(f"  参数变化: {convergence_info['parameter_change']:.6f}")
            
            # 检查收敛
            if convergence_info['converged']:
                if verbose:
                    print(f"  模型已收敛，提前停止训练")
                break
        
        if verbose:
            print("\n联邦训练完成!")
            print(f"最终全局模型准确率: {self.global_test_accuracies[-1]:.2f}%")
        
        return self.get_training_summary()
    
    def evaluate_global_model(self, test_dataloader):
        """
        评估全局模型
        
        Args:
            test_dataloader: 测试数据加载器
        """
        self.global_model.eval()
        test_loss = 0
        correct = 0
        total = 0
        
        criterion = torch.nn.CrossEntropyLoss()
        
        with torch.no_grad():
            for data, target in test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.global_model(data)
                test_loss += criterion(output, target).item()
                
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
        
        avg_loss = test_loss / len(test_dataloader)
        accuracy = 100. * correct / total
        
        return {
            'test_loss': avg_loss,
            'test_accuracy': accuracy
        }
    
    def get_global_model(self):
        """
        获取全局模型
        """
        return self.global_model
    
    def get_training_summary(self):
        """
        获取训练总结
        """
        if not self.round_history:
            return None
        
        return {
            'total_rounds': len(self.round_history),
            'final_accuracy': self.global_test_accuracies[-1],
            'final_loss': self.global_test_losses[-1],
            'accuracy_history': self.global_test_accuracies,
            'loss_history': self.global_test_losses,
            'round_history': self.round_history,
            'best_accuracy': max(self.global_test_accuracies),
            'best_round': self.global_test_accuracies.index(max(self.global_test_accuracies)) + 1
        }
    
    def save_model(self, filepath):
        """
        保存全局模型
        
        Args:
            filepath: 保存路径
        """
        torch.save(self.global_model.state_dict(), filepath)
        print(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath):
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
        """
        self.global_model.load_state_dict(torch.load(filepath))
        print(f"模型已从 {filepath} 加载")


if __name__ == "__main__":
    # 测试服务器
    from src.models.cnn_model import create_model
    
    # 创建服务器
    server = FederatedServer(create_model)
    print("联邦学习服务器创建成功")
    print(f"全局模型参数数量: {server.global_model.get_model_size():,}")
