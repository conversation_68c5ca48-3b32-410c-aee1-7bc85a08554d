"""
数据处理和分发模块
实现MNIST数据集的非均匀分布处理和客户端数据分配
"""

import torch
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader, Subset
import numpy as np
from collections import defaultdict
import random


class FederatedDataLoader:
    """
    联邦学习数据加载器
    """
    
    def __init__(self, num_clients=3, batch_size=32, iid=False):
        """
        初始化联邦数据加载器
        
        Args:
            num_clients: 客户端数量
            batch_size: 批次大小
            iid: 是否为独立同分布数据
        """
        self.num_clients = num_clients
        self.batch_size = batch_size
        self.iid = iid
        
        # 数据预处理
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.1307,), (0.3081,))
        ])
        
        # 加载MNIST数据集
        self.train_dataset = torchvision.datasets.MNIST(
            root='./data', train=True, download=True, transform=self.transform
        )
        
        self.test_dataset = torchvision.datasets.MNIST(
            root='./data', train=False, download=True, transform=self.transform
        )
        
        # 分配数据到各客户端
        self.client_data_indices = self._distribute_data()
        
    def _distribute_data(self):
        """
        将数据分配给各个客户端
        """
        if self.iid:
            return self._distribute_iid()
        else:
            return self._distribute_non_iid()
    
    def _distribute_iid(self):
        """
        独立同分布数据分配
        """
        num_samples = len(self.train_dataset)
        indices = list(range(num_samples))
        random.shuffle(indices)
        
        # 平均分配
        samples_per_client = num_samples // self.num_clients
        client_indices = []
        
        for i in range(self.num_clients):
            start_idx = i * samples_per_client
            end_idx = start_idx + samples_per_client
            client_indices.append(indices[start_idx:end_idx])
            
        return client_indices
    
    def _distribute_non_iid(self):
        """
        非独立同分布数据分配（按类别分配）
        """
        # 按类别组织数据
        class_indices = defaultdict(list)
        for idx, (_, label) in enumerate(self.train_dataset):
            class_indices[label].append(idx)
        
        # 为每个客户端分配不同的类别组合
        client_indices = [[] for _ in range(self.num_clients)]
        
        if self.num_clients == 3:
            # 客户端1: 主要包含数字0,1,2,3
            # 客户端2: 主要包含数字3,4,5,6
            # 客户端3: 主要包含数字6,7,8,9
            client_classes = [
                [0, 1, 2, 3],
                [3, 4, 5, 6], 
                [6, 7, 8, 9]
            ]
        else:
            # 通用分配策略
            classes_per_client = 10 // self.num_clients + 1
            client_classes = []
            for i in range(self.num_clients):
                start_class = (i * classes_per_client) % 10
                classes = [(start_class + j) % 10 for j in range(classes_per_client)]
                client_classes.append(classes)
        
        # 分配数据
        for client_id in range(self.num_clients):
            for class_id in client_classes[client_id]:
                # 每个客户端获得该类别的部分数据
                class_data = class_indices[class_id]
                samples_per_client = len(class_data) // len([c for c in client_classes if class_id in c])
                
                start_idx = client_id * samples_per_client
                end_idx = start_idx + samples_per_client
                
                if end_idx <= len(class_data):
                    client_indices[client_id].extend(class_data[start_idx:end_idx])
        
        # 随机打乱每个客户端的数据
        for indices in client_indices:
            random.shuffle(indices)
            
        return client_indices
    
    def get_client_dataloader(self, client_id):
        """
        获取指定客户端的数据加载器
        """
        if client_id >= self.num_clients:
            raise ValueError(f"客户端ID {client_id} 超出范围")
        
        client_dataset = Subset(self.train_dataset, self.client_data_indices[client_id])
        return DataLoader(client_dataset, batch_size=self.batch_size, shuffle=True)
    
    def get_test_dataloader(self):
        """
        获取测试数据加载器
        """
        return DataLoader(self.test_dataset, batch_size=self.batch_size, shuffle=False)
    
    def get_client_data_info(self):
        """
        获取各客户端数据分布信息
        """
        info = {}
        for client_id in range(self.num_clients):
            indices = self.client_data_indices[client_id]
            labels = [self.train_dataset[idx][1] for idx in indices]
            
            # 统计各类别数量
            class_counts = defaultdict(int)
            for label in labels:
                class_counts[label] += 1
            
            info[f'client_{client_id}'] = {
                'total_samples': len(indices),
                'class_distribution': dict(class_counts)
            }
        
        return info
    
    def print_data_distribution(self):
        """
        打印数据分布信息
        """
        info = self.get_client_data_info()
        print("=" * 50)
        print("数据分布信息")
        print("=" * 50)
        
        for client_name, client_info in info.items():
            print(f"\n{client_name.upper()}:")
            print(f"  总样本数: {client_info['total_samples']}")
            print(f"  类别分布: {client_info['class_distribution']}")


if __name__ == "__main__":
    # 测试数据加载器
    print("测试IID数据分布:")
    iid_loader = FederatedDataLoader(num_clients=3, iid=True)
    iid_loader.print_data_distribution()
    
    print("\n" + "="*50)
    print("测试Non-IID数据分布:")
    non_iid_loader = FederatedDataLoader(num_clients=3, iid=False)
    non_iid_loader.print_data_distribution()
