"""
评估工具模块
"""

import torch
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import pandas as pd


class FederatedLearningEvaluator:
    """
    联邦学习评估工具
    """
    
    def __init__(self, device='cpu'):
        """
        初始化评估工具
        
        Args:
            device: 计算设备
        """
        self.device = device
    
    def evaluate_model_detailed(self, model, test_dataloader, class_names=None):
        """
        详细评估模型性能
        
        Args:
            model: 要评估的模型
            test_dataloader: 测试数据加载器
            class_names: 类别名称列表
        
        Returns:
            详细评估结果字典
        """
        model.eval()
        all_predictions = []
        all_targets = []
        all_probabilities = []
        
        with torch.no_grad():
            for data, target in test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                output = model(data)
                
                # 获取预测结果
                probabilities = torch.softmax(output, dim=1)
                _, predicted = torch.max(output, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # 计算基本指标
        accuracy = np.mean(np.array(all_predictions) == np.array(all_targets)) * 100
        
        # 生成分类报告
        if class_names is None:
            class_names = [str(i) for i in range(10)]  # MNIST有10个类别
        
        classification_rep = classification_report(
            all_targets, all_predictions, 
            target_names=class_names, 
            output_dict=True
        )
        
        # 生成混淆矩阵
        conf_matrix = confusion_matrix(all_targets, all_predictions)
        
        return {
            'accuracy': accuracy,
            'predictions': all_predictions,
            'targets': all_targets,
            'probabilities': all_probabilities,
            'classification_report': classification_rep,
            'confusion_matrix': conf_matrix,
            'class_names': class_names
        }
    
    def plot_confusion_matrix(self, conf_matrix, class_names, title="混淆矩阵", save_path=None):
        """
        绘制混淆矩阵
        
        Args:
            conf_matrix: 混淆矩阵
            class_names: 类别名称
            title: 图表标题
            save_path: 保存路径
        """
        plt.figure(figsize=(10, 8))
        
        # 计算百分比
        conf_matrix_percent = conf_matrix.astype('float') / conf_matrix.sum(axis=1)[:, np.newaxis] * 100
        
        # 绘制热力图
        sns.heatmap(conf_matrix_percent, 
                   annot=True, 
                   fmt='.1f', 
                   cmap='Blues',
                   xticklabels=class_names,
                   yticklabels=class_names,
                   cbar_kws={'label': '百分比 (%)'})
        
        plt.title(title)
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_class_performance(self, evaluation_result):
        """
        分析各类别性能
        
        Args:
            evaluation_result: 评估结果
        
        Returns:
            类别性能分析结果
        """
        class_report = evaluation_result['classification_report']
        class_names = evaluation_result['class_names']
        
        # 提取各类别指标
        class_metrics = []
        for i, class_name in enumerate(class_names):
            if str(i) in class_report:
                metrics = class_report[str(i)]
                class_metrics.append({
                    'class': class_name,
                    'precision': metrics['precision'],
                    'recall': metrics['recall'],
                    'f1_score': metrics['f1-score'],
                    'support': metrics['support']
                })
        
        return pd.DataFrame(class_metrics)
    
    def plot_class_performance(self, class_performance_df, save_path=None):
        """
        绘制各类别性能图
        
        Args:
            class_performance_df: 类别性能DataFrame
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Precision
        axes[0, 0].bar(class_performance_df['class'], class_performance_df['precision'])
        axes[0, 0].set_title('各类别精确率')
        axes[0, 0].set_ylabel('精确率')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Recall
        axes[0, 1].bar(class_performance_df['class'], class_performance_df['recall'])
        axes[0, 1].set_title('各类别召回率')
        axes[0, 1].set_ylabel('召回率')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # F1-Score
        axes[1, 0].bar(class_performance_df['class'], class_performance_df['f1_score'])
        axes[1, 0].set_title('各类别F1分数')
        axes[1, 0].set_ylabel('F1分数')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Support
        axes[1, 1].bar(class_performance_df['class'], class_performance_df['support'])
        axes[1, 1].set_title('各类别样本数量')
        axes[1, 1].set_ylabel('样本数量')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_federated_vs_centralized(self, fed_model, cent_model, test_dataloader):
        """
        比较联邦学习和集中式学习模型
        
        Args:
            fed_model: 联邦学习模型
            cent_model: 集中式学习模型
            test_dataloader: 测试数据加载器
        
        Returns:
            比较结果
        """
        # 评估联邦学习模型
        fed_result = self.evaluate_model_detailed(fed_model, test_dataloader)
        
        # 评估集中式学习模型
        cent_result = self.evaluate_model_detailed(cent_model, test_dataloader)
        
        # 比较结果
        comparison = {
            'federated': {
                'accuracy': fed_result['accuracy'],
                'classification_report': fed_result['classification_report']
            },
            'centralized': {
                'accuracy': cent_result['accuracy'],
                'classification_report': cent_result['classification_report']
            },
            'difference': {
                'accuracy_diff': fed_result['accuracy'] - cent_result['accuracy']
            }
        }
        
        return comparison, fed_result, cent_result
    
    def analyze_training_efficiency(self, federated_summary):
        """
        分析训练效率
        
        Args:
            federated_summary: 联邦学习训练总结
        
        Returns:
            训练效率分析结果
        """
        accuracy_history = federated_summary['accuracy_history']
        
        # 计算收敛轮数（达到最终准确率95%的轮数）
        final_accuracy = accuracy_history[-1]
        convergence_threshold = final_accuracy * 0.95
        
        convergence_round = None
        for i, acc in enumerate(accuracy_history):
            if acc >= convergence_threshold:
                convergence_round = i + 1
                break
        
        # 计算平均改善率
        improvements = []
        for i in range(1, len(accuracy_history)):
            improvement = accuracy_history[i] - accuracy_history[i-1]
            improvements.append(improvement)
        
        avg_improvement = np.mean(improvements) if improvements else 0
        
        # 计算稳定性（最后几轮的标准差）
        last_rounds = min(5, len(accuracy_history))
        stability = np.std(accuracy_history[-last_rounds:]) if last_rounds > 1 else 0
        
        return {
            'convergence_round': convergence_round,
            'total_rounds': len(accuracy_history),
            'final_accuracy': final_accuracy,
            'best_accuracy': max(accuracy_history),
            'average_improvement_per_round': avg_improvement,
            'training_stability': stability,
            'efficiency_score': (final_accuracy / len(accuracy_history)) if len(accuracy_history) > 0 else 0
        }
    
    def generate_evaluation_report(self, federated_summary, centralized_result, 
                                 fed_model, test_dataloader, save_dir='results'):
        """
        生成完整的评估报告
        
        Args:
            federated_summary: 联邦学习训练总结
            centralized_result: 集中式学习结果
            fed_model: 联邦学习模型
            test_dataloader: 测试数据加载器
            save_dir: 保存目录
        """
        print("=" * 60)
        print("联邦学习评估报告")
        print("=" * 60)
        
        # 1. 基本性能对比
        print("\n1. 基本性能对比:")
        print(f"   联邦学习准确率: {federated_summary['final_accuracy']:.2f}%")
        print(f"   集中式学习准确率: {centralized_result['accuracy']:.2f}%")
        print(f"   准确率差异: {federated_summary['final_accuracy'] - centralized_result['accuracy']:.2f}%")
        
        # 2. 训练效率分析
        efficiency = self.analyze_training_efficiency(federated_summary)
        print(f"\n2. 训练效率分析:")
        print(f"   收敛轮数: {efficiency['convergence_round']}")
        print(f"   总训练轮数: {efficiency['total_rounds']}")
        print(f"   平均每轮改善: {efficiency['average_improvement_per_round']:.3f}%")
        print(f"   训练稳定性: {efficiency['training_stability']:.3f}")
        print(f"   效率分数: {efficiency['efficiency_score']:.3f}")
        
        # 3. 详细模型评估
        fed_evaluation = self.evaluate_model_detailed(fed_model, test_dataloader)
        class_performance = self.analyze_class_performance(fed_evaluation)
        
        print(f"\n3. 各类别性能 (联邦学习模型):")
        print(class_performance.to_string(index=False))
        
        # 4. 绘制图表
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        # 混淆矩阵
        self.plot_confusion_matrix(
            fed_evaluation['confusion_matrix'],
            fed_evaluation['class_names'],
            title="联邦学习模型混淆矩阵",
            save_path=os.path.join(save_dir, 'confusion_matrix.png')
        )
        
        # 类别性能图
        self.plot_class_performance(
            class_performance,
            save_path=os.path.join(save_dir, 'class_performance.png')
        )
        
        print(f"\n评估报告完成，图表已保存到 {save_dir} 目录")
        
        return {
            'basic_comparison': {
                'federated_accuracy': federated_summary['final_accuracy'],
                'centralized_accuracy': centralized_result['accuracy'],
                'accuracy_difference': federated_summary['final_accuracy'] - centralized_result['accuracy']
            },
            'efficiency_analysis': efficiency,
            'detailed_evaluation': fed_evaluation,
            'class_performance': class_performance
        }


if __name__ == "__main__":
    # 测试评估工具
    evaluator = FederatedLearningEvaluator()
    print("评估工具创建成功")
