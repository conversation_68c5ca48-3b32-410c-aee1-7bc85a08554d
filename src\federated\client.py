"""
联邦学习客户端实现
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import copy
from tqdm import tqdm


class FederatedClient:
    """
    联邦学习客户端
    """
    
    def __init__(self, client_id, model, dataloader, device='cpu', lr=0.01):
        """
        初始化客户端
        
        Args:
            client_id: 客户端ID
            model: 模型实例
            dataloader: 数据加载器
            device: 计算设备
            lr: 学习率
        """
        self.client_id = client_id
        self.model = model.to(device)
        self.dataloader = dataloader
        self.device = device
        self.lr = lr
        
        # 损失函数和优化器
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = optim.SGD(self.model.parameters(), lr=self.lr, momentum=0.9)
        
        # 训练历史
        self.train_losses = []
        self.train_accuracies = []
        
    def set_model_parameters(self, parameters):
        """
        设置模型参数（从服务器接收）
        """
        self.model.set_model_parameters(parameters)
        
    def get_model_parameters(self):
        """
        获取模型参数（发送给服务器）
        """
        return self.model.get_model_parameters()
    
    def train_local_model(self, epochs=1, verbose=False):
        """
        本地训练模型
        
        Args:
            epochs: 训练轮数
            verbose: 是否显示详细信息
        """
        self.model.train()
        epoch_losses = []
        epoch_accuracies = []
        
        for epoch in range(epochs):
            running_loss = 0.0
            correct = 0
            total = 0
            
            # 使用tqdm显示进度条
            dataloader_iter = tqdm(self.dataloader, desc=f"Client {self.client_id} Epoch {epoch+1}/{epochs}") if verbose else self.dataloader
            
            for batch_idx, (data, target) in enumerate(dataloader_iter):
                data, target = data.to(self.device), target.to(self.device)
                
                # 前向传播
                self.optimizer.zero_grad()
                output = self.model(data)
                loss = self.criterion(output, target)
                
                # 反向传播
                loss.backward()
                self.optimizer.step()
                
                # 统计
                running_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
                
                if verbose and batch_idx % 100 == 0:
                    dataloader_iter.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'Acc': f'{100.*correct/total:.2f}%'
                    })
            
            # 计算平均损失和准确率
            avg_loss = running_loss / len(self.dataloader)
            accuracy = 100. * correct / total
            
            epoch_losses.append(avg_loss)
            epoch_accuracies.append(accuracy)
            
            if verbose:
                print(f"Client {self.client_id} - Epoch {epoch+1}: Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%")
        
        # 更新训练历史
        self.train_losses.extend(epoch_losses)
        self.train_accuracies.extend(epoch_accuracies)
        
        return {
            'losses': epoch_losses,
            'accuracies': epoch_accuracies,
            'final_loss': epoch_losses[-1],
            'final_accuracy': epoch_accuracies[-1]
        }
    
    def evaluate_model(self, test_dataloader):
        """
        评估模型性能
        
        Args:
            test_dataloader: 测试数据加载器
        """
        self.model.eval()
        test_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                test_loss += self.criterion(output, target).item()
                
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
        
        avg_loss = test_loss / len(test_dataloader)
        accuracy = 100. * correct / total
        
        return {
            'test_loss': avg_loss,
            'test_accuracy': accuracy
        }
    
    def get_data_size(self):
        """
        获取客户端数据大小
        """
        return len(self.dataloader.dataset)
    
    def get_training_history(self):
        """
        获取训练历史
        """
        return {
            'losses': self.train_losses,
            'accuracies': self.train_accuracies
        }
    
    def reset_training_history(self):
        """
        重置训练历史
        """
        self.train_losses = []
        self.train_accuracies = []


def create_clients(model_class, data_loader, num_clients, device='cpu', lr=0.01):
    """
    创建多个客户端
    
    Args:
        model_class: 模型类
        data_loader: 联邦数据加载器
        num_clients: 客户端数量
        device: 计算设备
        lr: 学习率
    """
    clients = []
    
    for client_id in range(num_clients):
        # 为每个客户端创建独立的模型实例
        model = model_class()
        client_dataloader = data_loader.get_client_dataloader(client_id)
        
        client = FederatedClient(
            client_id=client_id,
            model=model,
            dataloader=client_dataloader,
            device=device,
            lr=lr
        )
        
        clients.append(client)
    
    return clients


if __name__ == "__main__":
    # 测试客户端
    from src.models.cnn_model import create_model
    from src.data.data_loader import FederatedDataLoader
    
    # 创建数据加载器和模型
    data_loader = FederatedDataLoader(num_clients=3, iid=False)
    model = create_model()
    
    # 创建客户端
    client = FederatedClient(
        client_id=0,
        model=model,
        dataloader=data_loader.get_client_dataloader(0),
        device='cpu'
    )
    
    print(f"客户端数据大小: {client.get_data_size()}")
    
    # 测试训练
    results = client.train_local_model(epochs=1, verbose=True)
    print(f"训练结果: {results}")
