"""
可视化工具模块
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from collections import defaultdict
import os


# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


class FederatedLearningVisualizer:
    """
    联邦学习可视化工具
    """
    
    def __init__(self, save_dir='results/plots'):
        """
        初始化可视化工具
        
        Args:
            save_dir: 图片保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
    
    def plot_training_curves(self, federated_summary, centralized_result=None, save_name=None):
        """
        绘制训练曲线
        
        Args:
            federated_summary: 联邦学习训练总结
            centralized_result: 集中式学习结果（可选）
            save_name: 保存文件名
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        rounds = list(range(1, len(federated_summary['accuracy_history']) + 1))
        
        # 绘制准确率曲线
        ax1.plot(rounds, federated_summary['accuracy_history'], 
                'b-o', label='联邦学习', linewidth=2, markersize=6)
        
        if centralized_result:
            ax1.axhline(y=centralized_result['accuracy'], color='r', linestyle='--', 
                       label=f'集中式学习 ({centralized_result["accuracy"]:.2f}%)', linewidth=2)
        
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('测试准确率 (%)')
        ax1.set_title('模型准确率变化')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 绘制损失曲线
        ax2.plot(rounds, federated_summary['loss_history'], 
                'g-o', label='联邦学习', linewidth=2, markersize=6)
        
        if centralized_result:
            ax2.axhline(y=centralized_result['loss'], color='r', linestyle='--', 
                       label=f'集中式学习 ({centralized_result["loss"]:.4f})', linewidth=2)
        
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('测试损失')
        ax2.set_title('模型损失变化')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_name:
            plt.savefig(os.path.join(self.save_dir, f'{save_name}_training_curves.png'), 
                       dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_data_distribution(self, data_loader, save_name=None):
        """
        绘制数据分布图
        
        Args:
            data_loader: 联邦数据加载器
            save_name: 保存文件名
        """
        data_info = data_loader.get_client_data_info()
        
        # 准备数据
        clients = []
        classes = []
        counts = []
        
        for client_name, info in data_info.items():
            for class_id, count in info['class_distribution'].items():
                clients.append(client_name.replace('client_', 'Client '))
                classes.append(f'Class {class_id}')
                counts.append(count)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Client': clients,
            'Class': classes,
            'Count': counts
        })
        
        # 绘制堆叠柱状图
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 透视表
        pivot_df = df.pivot(index='Client', columns='Class', values='Count').fillna(0)
        
        # 绘制堆叠柱状图
        pivot_df.plot(kind='bar', stacked=True, ax=ax, colormap='tab10')
        
        ax.set_title('各客户端数据分布', fontsize=16)
        ax.set_xlabel('客户端', fontsize=12)
        ax.set_ylabel('样本数量', fontsize=12)
        ax.legend(title='数字类别', bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.xticks(rotation=0)
        plt.tight_layout()
        
        if save_name:
            plt.savefig(os.path.join(self.save_dir, f'{save_name}_data_distribution.png'), 
                       dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_client_performance(self, federated_summary, save_name=None):
        """
        绘制各客户端性能对比
        
        Args:
            federated_summary: 联邦学习训练总结
            save_name: 保存文件名
        """
        # 收集各客户端的性能数据
        client_performance = defaultdict(list)
        
        for round_info in federated_summary['round_history']:
            for i, client_result in enumerate(round_info['client_results']):
                client_id = round_info['selected_clients'][i]
                client_performance[client_id].append(client_result['final_accuracy'])
        
        # 绘制图表
        fig, ax = plt.subplots(figsize=(12, 8))
        
        for client_id, accuracies in client_performance.items():
            rounds = list(range(1, len(accuracies) + 1))
            ax.plot(rounds, accuracies, '-o', label=f'Client {client_id}', 
                   linewidth=2, markersize=6)
        
        ax.set_xlabel('参与轮数')
        ax.set_ylabel('本地训练准确率 (%)')
        ax.set_title('各客户端本地训练性能')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_name:
            plt.savefig(os.path.join(self.save_dir, f'{save_name}_client_performance.png'), 
                       dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_convergence_analysis(self, federated_summary, save_name=None):
        """
        绘制收敛性分析图
        
        Args:
            federated_summary: 联邦学习训练总结
            save_name: 保存文件名
        """
        # 提取参数变化数据
        parameter_changes = []
        rounds = []
        
        for round_info in federated_summary['round_history']:
            if round_info['convergence_info']['parameter_change'] is not None:
                parameter_changes.append(round_info['convergence_info']['parameter_change'])
                rounds.append(round_info['round'])
        
        if not parameter_changes:
            print("没有收敛数据可供绘制")
            return
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 参数变化图
        ax1.plot(rounds, parameter_changes, 'r-o', linewidth=2, markersize=6)
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('参数变化 (L2范数)')
        ax1.set_title('模型参数收敛性分析')
        ax1.set_yscale('log')
        ax1.grid(True, alpha=0.3)
        
        # 准确率改善图
        accuracy_improvements = []
        for i in range(1, len(federated_summary['accuracy_history'])):
            improvement = (federated_summary['accuracy_history'][i] - 
                          federated_summary['accuracy_history'][i-1])
            accuracy_improvements.append(improvement)
        
        improvement_rounds = list(range(2, len(federated_summary['accuracy_history']) + 1))
        ax2.plot(improvement_rounds, accuracy_improvements, 'g-o', linewidth=2, markersize=6)
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('准确率改善 (%)')
        ax2.set_title('每轮准确率改善情况')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_name:
            plt.savefig(os.path.join(self.save_dir, f'{save_name}_convergence_analysis.png'), 
                       dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_summary_report(self, federated_summary, centralized_result, config, save_name=None):
        """
        创建实验总结报告图
        
        Args:
            federated_summary: 联邦学习结果
            centralized_result: 集中式学习结果
            config: 实验配置
            save_name: 保存文件名
        """
        fig = plt.figure(figsize=(16, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. 准确率对比
        ax1 = fig.add_subplot(gs[0, 0])
        methods = ['联邦学习', '集中式学习']
        accuracies = [federated_summary['final_accuracy'], centralized_result['accuracy']]
        colors = ['skyblue', 'lightcoral']
        
        bars = ax1.bar(methods, accuracies, color=colors)
        ax1.set_ylabel('准确率 (%)')
        ax1.set_title('最终准确率对比')
        
        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    f'{acc:.2f}%', ha='center', va='bottom')
        
        # 2. 训练曲线
        ax2 = fig.add_subplot(gs[0, 1:])
        rounds = list(range(1, len(federated_summary['accuracy_history']) + 1))
        ax2.plot(rounds, federated_summary['accuracy_history'], 'b-o', label='联邦学习')
        ax2.axhline(y=centralized_result['accuracy'], color='r', linestyle='--', label='集中式学习')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('测试准确率 (%)')
        ax2.set_title('训练过程对比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 实验配置信息
        ax3 = fig.add_subplot(gs[1, :])
        ax3.axis('off')
        
        config_text = f"""
实验配置信息:
• 客户端数量: {config['num_clients']}
• 训练轮数: {config['num_rounds']}
• 本地训练轮数: {config['local_epochs']}
• 批次大小: {config['batch_size']}
• 学习率: {config['learning_rate']}
• 数据分布: {'IID' if config['iid'] else 'Non-IID'}
• 客户端参与比例: {config['client_fraction']}

实验结果:
• 联邦学习最终准确率: {federated_summary['final_accuracy']:.2f}%
• 集中式学习准确率: {centralized_result['accuracy']:.2f}%
• 准确率差异: {federated_summary['final_accuracy'] - centralized_result['accuracy']:.2f}%
• 最佳轮次: {federated_summary['best_round']}
• 最佳准确率: {federated_summary['best_accuracy']:.2f}%
        """
        
        ax3.text(0.1, 0.9, config_text, transform=ax3.transAxes, fontsize=11,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        # 4. 损失曲线
        ax4 = fig.add_subplot(gs[2, :])
        ax4.plot(rounds, federated_summary['loss_history'], 'g-o', label='联邦学习')
        ax4.axhline(y=centralized_result['loss'], color='r', linestyle='--', label='集中式学习')
        ax4.set_xlabel('训练轮数')
        ax4.set_ylabel('测试损失')
        ax4.set_title('损失变化对比')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle('联邦学习实验总结报告', fontsize=16, fontweight='bold')
        
        if save_name:
            plt.savefig(os.path.join(self.save_dir, f'{save_name}_summary_report.png'), 
                       dpi=300, bbox_inches='tight')
        plt.show()


if __name__ == "__main__":
    # 测试可视化工具
    visualizer = FederatedLearningVisualizer()
    print("可视化工具创建成功")
